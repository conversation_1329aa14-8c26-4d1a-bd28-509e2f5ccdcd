/**
 * Country Selector Utility
 * 
 * Allows users to browse eBay from different countries' perspectives
 * Overrides automatic geolocation detection when user makes a manual selection
 */

export interface CountryConfig {
    country: string;
    countryName: string;
    postalCode: string;
    city: string;
    flag: string;
    description: string;
    marketType: 'primary' | 'secondary' | 'limited';
    localPickupAvailable: boolean;
    currency?: string;
    language?: string;
    /** Internationalization support for future use */
    i18n?: {
        /** Localized country name for different languages */
        localizedNames?: Record<string, string>;
        /** Localized descriptions for different languages */
        localizedDescriptions?: Record<string, string>;
        /** Example search terms in local language */
        exampleSearchTerms?: string[];
    };
}

/**
 * Available countries for user selection
 * Organized by popularity and eBay market presence
 */
export const AVAILABLE_COUNTRIES: Record<string, CountryConfig> = {
    // Major eBay Markets
    'US': {
        country: 'US',
        countryName: 'United States',
        postalCode: '94107',
        city: 'San Francisco',
        flag: '🇺🇸',
        description: 'Browse eBay.com with local pickup and US shipping',
        marketType: 'primary',
        localPickupAvailable: true,
        currency: 'USD',
        language: 'en',
    },
    'GB': {
        country: 'GB',
        countryName: 'United Kingdom',
        postalCode: 'SW1A 1AA',
        city: 'London',
        flag: '🇬🇧',
        description: 'Browse eBay.co.uk with UK shipping and collection',
        marketType: 'primary',
        localPickupAvailable: true,
        currency: 'GBP',
        language: 'en',
    },
    'DE': {
        country: 'DE',
        countryName: 'Germany',
        postalCode: '10115',
        city: 'Berlin',
        flag: '🇩🇪',
        description: 'Browse eBay.de with German shipping and EU options',
        marketType: 'primary',
        localPickupAvailable: true,
        currency: 'EUR',
        language: 'de',
    },
    'CA': {
        country: 'CA',
        countryName: 'Canada',
        postalCode: 'M5V 3A8',
        city: 'Toronto',
        flag: '🇨🇦',
        description: 'Browse eBay.ca with Canadian shipping options',
        marketType: 'primary',
        localPickupAvailable: true,
        currency: 'CAD',
        language: 'en',
    },
    'AU': {
        country: 'AU',
        countryName: 'Australia',
        postalCode: '2000',
        city: 'Sydney',
        flag: '🇦🇺',
        description: 'Browse eBay.com.au with Australian shipping',
        marketType: 'primary',
        localPickupAvailable: true,
        currency: 'AUD',
        language: 'en',
    },
    'FR': {
        country: 'FR',
        countryName: 'France',
        postalCode: '75001',
        city: 'Paris',
        flag: '🇫🇷',
        description: 'Browse eBay.fr with French and EU shipping',
        marketType: 'primary', // France is a major eBay market
        localPickupAvailable: true, // France has EBAY_FR marketplace
        currency: 'EUR',
        language: 'fr',
    },
    'IT': {
        country: 'IT',
        countryName: 'Italy',
        postalCode: '00118',
        city: 'Rome',
        flag: '🇮🇹',
        description: 'Browse eBay.it with Italian and EU shipping',
        marketType: 'primary', // Italy is a major eBay market
        localPickupAvailable: true, // Italy has EBAY_IT marketplace
        currency: 'EUR',
        language: 'it',
    },
    'ES': {
        country: 'ES',
        countryName: 'Spain',
        postalCode: '28001',
        city: 'Madrid',
        flag: '🇪🇸',
        description: 'Browse eBay.es with Spanish and EU shipping',
        marketType: 'primary', // Spain is a major eBay market
        localPickupAvailable: true, // Spain has EBAY_ES marketplace
        currency: 'EUR',
        language: 'es',
    },
    'NL': {
        country: 'NL',
        countryName: 'Netherlands',
        postalCode: '1012',
        city: 'Amsterdam',
        flag: '🇳🇱',
        description: 'Browse eBay.nl with Dutch and EU shipping',
        marketType: 'primary', // Netherlands is a major eBay market
        localPickupAvailable: true, // Netherlands has EBAY_NL marketplace
        currency: 'EUR',
        language: 'nl',
    },
    // European Markets
    'BE': {
        country: 'BE',
        countryName: 'Belgium',
        postalCode: '1000',
        city: 'Brussels',
        flag: '🇧🇪',
        description: 'Browse eBay.be with Belgian and EU shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'EUR',
        language: 'nl', // Primary language, also supports French
    },
    'AT': {
        country: 'AT',
        countryName: 'Austria',
        postalCode: '1010',
        city: 'Vienna',
        flag: '🇦🇹',
        description: 'Browse eBay.at with Austrian and EU shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'EUR',
        language: 'de',
    },
    'CH': {
        country: 'CH',
        countryName: 'Switzerland',
        postalCode: '8001',
        city: 'Zurich',
        flag: '🇨🇭',
        description: 'Browse eBay.ch with Swiss shipping options',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'CHF',
        language: 'de',
    },
    'IE': {
        country: 'IE',
        countryName: 'Ireland',
        postalCode: 'D01',
        city: 'Dublin',
        flag: '🇮🇪',
        description: 'Browse eBay.ie with Irish and EU shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'EUR',
        language: 'en',
    },
    'PL': {
        country: 'PL',
        countryName: 'Poland',
        postalCode: '00-001',
        city: 'Warsaw',
        flag: '🇵🇱',
        description: 'Browse eBay.pl with Polish and EU shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'PLN',
        language: 'pl',
    },

    // Asian Markets
    'HK': {
        country: 'HK',
        countryName: 'Hong Kong',
        postalCode: '999077',
        city: 'Hong Kong',
        flag: '🇭🇰',
        description: 'Browse eBay.com.hk with Hong Kong shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'HKD',
        language: 'zh',
    },
    'SG': {
        country: 'SG',
        countryName: 'Singapore',
        postalCode: '018989',
        city: 'Singapore',
        flag: '🇸🇬',
        description: 'Browse eBay.com.sg with Singapore shipping',
        marketType: 'secondary',
        localPickupAvailable: true,
        currency: 'SGD',
        language: 'en',
    },


    // Limited Markets (no dedicated eBay marketplace - use EBAY_US with international shipping)
    'JP': {
        country: 'JP',
        countryName: 'Japan',
        postalCode: '100-0001',
        city: 'Tokyo',
        flag: '🇯🇵',
        description: 'Browse eBay with worldwide shipping to Japan',
        marketType: 'limited',
        localPickupAvailable: false,
        currency: 'JPY',
        language: 'ja',
        i18n: {
            localizedNames: {
                'ja': '日本',
                'en': 'Japan',
            },
            localizedDescriptions: {
                'ja': '日本への国際配送でeBayを閲覧',
                'en': 'Browse eBay with worldwide shipping to Japan',
            },
            exampleSearchTerms: ['ヴィンテージカメラ', 'アンティーク時計', 'コレクション'],
        },
    },
    'MY': {
        country: 'MY',
        countryName: 'Malaysia',
        postalCode: '50088',
        city: 'Kuala Lumpur',
        flag: '🇲🇾',
        description: 'Browse eBay with worldwide shipping to Malaysia',
        marketType: 'limited',
        localPickupAvailable: false,
        currency: 'MYR',
        language: 'en',
    },
    'PH': {
        country: 'PH',
        countryName: 'Philippines',
        postalCode: '1000',
        city: 'Manila',
        flag: '🇵🇭',
        description: 'Browse eBay with worldwide shipping to Philippines',
        marketType: 'limited',
        localPickupAvailable: false,
        currency: 'PHP',
        language: 'en',
    },
    'TW': {
        country: 'TW',
        countryName: 'Taiwan',
        postalCode: '100',
        city: 'Taipei',
        flag: '🇹🇼',
        description: 'Browse eBay with worldwide shipping to Taiwan',
        marketType: 'limited',
        localPickupAvailable: false,
        currency: 'TWD',
        language: 'zh',
    },
};

/**
 * Get user's current country selection (URL override or auto-detected)
 */
export function getUserCountrySelection(urlParams?: URLSearchParams | string, autoDetectedCountry?: string): CountryConfig {
    // Check for manual user selection in URL first
    const manualSelection = getManualCountrySelection(urlParams);
    if (manualSelection) {
        return manualSelection;
    }

    // Fall back to auto-detected country if it's supported
    if (autoDetectedCountry && AVAILABLE_COUNTRIES[autoDetectedCountry]) {
        return AVAILABLE_COUNTRIES[autoDetectedCountry]!;
    }

    // Default to US if no valid selection - guaranteed to exist
    return AVAILABLE_COUNTRIES.US!;
}

/**
 * Get manual country selection from URL parameters
 */
export function getManualCountrySelection(urlParams?: URLSearchParams | string): CountryConfig | null {
    let params: URLSearchParams;

    if (typeof urlParams === 'string') {
        params = new URLSearchParams(urlParams);
    } else if (urlParams instanceof URLSearchParams) {
        params = urlParams;
    } else if (typeof window !== 'undefined') {
        params = new URLSearchParams(window.location.search);
    } else {
        return null;
    }

    const selectedCountry = params.get('from');

    if (selectedCountry && AVAILABLE_COUNTRIES[selectedCountry]) {
        return AVAILABLE_COUNTRIES[selectedCountry];
    }

    return null;
}

/**
 * Set user's manual country selection in URL
 */
export function setCountrySelection(countryCode: string, currentUrl?: string): string {
    if (!AVAILABLE_COUNTRIES[countryCode]) {
        throw new Error(`Invalid country code: ${countryCode}`);
    }

    const url = new URL(currentUrl || window.location.href);
    url.searchParams.set('from', countryCode);
    return url.toString();
}

/**
 * Clear user's manual country selection (go back to auto-detection)
 */
export function clearCountrySelection(currentUrl?: string): string {
    const url = new URL(currentUrl || window.location.href);
    url.searchParams.delete('from');
    return url.toString();
}

/**
 * Check if user has made a manual country selection
 */
export function hasManualSelection(urlParams?: URLSearchParams | string): boolean {
    return getManualCountrySelection(urlParams) !== null;
}

/**
 * Get countries grouped by market type for UI display
 */
export function getCountriesGrouped() {
    const grouped = {
        primary: [] as CountryConfig[],
        secondary: [] as CountryConfig[],
        limited: [] as CountryConfig[],
    };

    Object.values(AVAILABLE_COUNTRIES).forEach(country => {
        grouped[country.marketType].push(country);
    });

    // Sort each group alphabetically
    Object.keys(grouped).forEach(key => {
        grouped[key as keyof typeof grouped].sort((a, b) => a.countryName.localeCompare(b.countryName));
    });

    return grouped;
}

/**
 * Get localized country name and description
 * @param countryCode ISO 2-letter country code
 * @param locale Language code (e.g., 'en', 'ja', 'de')
 * @returns Localized country information
 */
export function getLocalizedCountryInfo(countryCode: string, locale: string = 'en'): {
    name: string;
    description: string;
    exampleSearchTerms?: string[];
} {
    const country = AVAILABLE_COUNTRIES[countryCode];
    if (!country) {
        return {
            name: countryCode,
            description: 'Country not found',
        };
    }

    const localizedName = country.i18n?.localizedNames?.[locale] || country.countryName;
    const localizedDescription = country.i18n?.localizedDescriptions?.[locale] || country.description;
    const exampleSearchTerms = country.i18n?.exampleSearchTerms;

    return {
        name: localizedName,
        description: localizedDescription,
        exampleSearchTerms,
    };
}

