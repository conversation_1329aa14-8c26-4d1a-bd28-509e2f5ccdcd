import { MarketplaceId, A<PERSON><PERSON><PERSON><PERSON>_MARKETPLACES } from '@/lib/ebay/types/marketplaces';
import { logger } from '@/lib/logger';

const log = logger.create('utils:country-marketplace-mapping');

/**
 * Map country codes to eBay marketplace IDs
 * Only includes countries with official eBay Buy API marketplace support
 * Based on: https://developer.ebay.com/api-docs/buy/browse/overview.html
 *
 * Countries without dedicated marketplaces will fall back to EBAY_US
 */
const COUNTRY_TO_MARKETPLACE: Record<string, MarketplaceId> = {
  // Official eBay Buy API supported marketplaces
  US: 'EBAY_US',
  GB: 'EBAY_GB',
  UK: 'EBAY_GB', // Alias for GB
  CA: 'EBAY_CA',
  AU: 'EBAY_AU',
  DE: 'EBAY_DE',
  FR: 'EBAY_FR',
  IT: 'EBAY_IT',
  ES: 'EBAY_ES',
  NL: 'EBAY_NL',
  BE: 'EBAY_BE',
  AT: 'EBAY_AT',
  CH: 'EBAY_CH',
  IE: 'EBAY_IE',
  PL: 'EBAY_PL',
  HK: 'EBAY_HK',
  SG: 'EBAY_SG',
  // Note: MY, PH, TW are not supported by eBay Buy API
  // These countries will fall back to EBAY_US
};

/**
 * Get the appropriate eBay marketplace for a country code
 * @param countryCode ISO 2-letter country code (e.g., 'US', 'GB', 'DE')
 * @param fallback Fallback marketplace if country is not mapped (defaults to EBAY_US)
 * @returns MarketplaceId for the country, or fallback if no direct marketplace exists
 */
export function getMarketplaceForCountry(
  countryCode: string,
  fallback: MarketplaceId = 'EBAY_US',
): MarketplaceId {
  if (!countryCode) {
    log.debug('No country code provided, using fallback', { fallback });
    return fallback;
  }

  const upperCountryCode = countryCode.toUpperCase();
  const marketplace = COUNTRY_TO_MARKETPLACE[upperCountryCode];

  if (marketplace) {
    log.debug('Mapped country to established marketplace', {
      countryCode: upperCountryCode,
      marketplace,
    });
    return marketplace;
  }

  log.debug('Country does not have established eBay marketplace, using fallback', {
    countryCode: upperCountryCode,
    fallback,
  });
  return fallback;
}

/**
 * Check if a country has local pickup support
 * @param countryCode ISO 2-letter country code
 * @returns true if the country has an established eBay marketplace suitable for local pickup
 */
export function supportsLocalPickup(countryCode: string): boolean {
  if (!countryCode) {
    return false;
  }

  const upperCountryCode = countryCode.toUpperCase();
  return upperCountryCode in COUNTRY_TO_MARKETPLACE;
}

/**
 * Get universal pickup radius configuration 
 * @returns Object with radius and unit (80km, ~1 hour drive)
 */
export function getPickupConfigForCountry(): {
  radius: number;
  unit: 'mi' | 'km';
  description: string;
} {
  // Use universal 80km (~50 miles, ~1 hour drive) for all countries
  // This simplifies the logic and provides reasonable pickup range globally
  return {
    radius: 80,
    unit: 'km',
    description: '80 km radius (~1 hour drive)',
  };
}

/**
 * Check if a country has a dedicated eBay marketplace
 * @param countryCode ISO 2-letter country code
 * @returns true if the country has its own established eBay marketplace
 */
export function hasDirectMarketplace(countryCode: string): boolean {
  return supportsLocalPickup(countryCode);
}

/**
 * Get user-friendly marketplace name for a country
 * @param countryCode ISO 2-letter country code
 * @returns Human-readable marketplace name
 */
export function getMarketplaceNameForCountry(countryCode: string): string {
  const marketplace = getMarketplaceForCountry(countryCode);
  const marketplaceInfo = AVAILABLE_MARKETPLACES.find((m) => m.id === marketplace);

  if (marketplaceInfo) {
    return marketplaceInfo.name;
  }

  return 'International'; // Fallback
}

/**
 * Get a user-friendly message about local pickup availability
 * @param countryCode ISO 2-letter country code
 * @returns Object with availability status and user message
 */
export function getLocalPickupAvailability(countryCode: string): {
  available: boolean;
  message: string;
  marketplaceName?: string;
} {
  if (!countryCode) {
    return {
      available: false,
      message: 'Location not detected - local pickup not available',
    };
  }

  if (supportsLocalPickup(countryCode)) {
    const marketplaceName = getMarketplaceNameForCountry(countryCode);
    return {
      available: true,
      message: `Local pickup available in ${marketplaceName}`,
      marketplaceName,
    };
  }

  return {
    available: false,
    message: 'Local pickup not available in your region - try browsing all delivery options',
  };
}

/**
 * Export the mapping for testing or advanced use cases
 */
export { COUNTRY_TO_MARKETPLACE };
