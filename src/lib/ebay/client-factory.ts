import { ebaySellAccountV1Client } from '@ebay-sdk/sell/account/v1/Client';
import { ebaySellMetadataClient } from '@ebay-sdk/sell/metadata/v1/Client';
import { logger } from '@/lib/logger';
import { env } from '@/env.mjs';
import { ebayCommerceIdentityClient } from '@ebay-sdk/commerce/identity/v1/Client';
import { ebayCommerceTaxonomyClient } from '@ebay-sdk/commerce/taxonomy/v1/Client';
import { ebayCommerceCatalogBetaClient } from '@ebay-sdk/commerce/catalog/v1_beta/Client';
import { ebayCommerceMediaBetaClient } from '@ebay-sdk/commerce/media/v1_beta/Client';
import { ebayBuyBrowseClient } from '@ebay-sdk/buy/browse/v1/Client';
import { ebayCommerceIdentityEnvironment } from '@ebay-sdk/commerce/identity/v1/environments';
import { ebaySellAccountV1Environment } from '@ebay-sdk/sell/account/v1/environments';
import { ebaySellMetadataEnvironment } from '@ebay-sdk/sell/metadata/v1/environments';
import { ebayCommerceTaxonomyEnvironment } from '@ebay-sdk/commerce/taxonomy/v1/environments';
import { ebayCommerceCatalogBetaEnvironment } from '@ebay-sdk/commerce/catalog/v1_beta/environments';
import { ebayCommerceMediaBetaEnvironment } from '@ebay-sdk/commerce/media/v1_beta/environments';
import { ebayBuyBrowseEnvironment } from '@ebay-sdk/buy/browse/v1/environments';
import { EbayOAuth } from './oauth';
import { getEbayCredentials } from './server/auth-operations';
import { getOrRefreshEbayAppCredentials } from './server/app-auth-operations';
import { storeEbayResponse } from '@/lib/ebay/server/api-operations';
import { LocaleCode } from './types/marketplaces';
import { EbayHeaders } from './types/headers';
import { handleEbayApiError } from './utils/error-handlers';

const log = logger.create('ebay:client-factory');

/**
 * =============================================================================
 *  eBay REST SDK – Client Factory (User-scoped & App-scoped)
 * =============================================================================
 *  This file provides a unified factory for creating eBay API SDK clients with
 *  robust authentication, token refresh, header management, logging, and error
 *  handling. The eBay SDK clients generated by Fern are thin wrappers and do not
 *  handle these concerns out of the box.
 *
 *  This factory exposes two main flavors:
 *    1. `createEbayClient` – For user-scoped clients (requires per-user OAuth tokens).
 *    2. `createEbayAppClient` – For app-scoped clients (uses client credentials).
 *
 *  Key features:
 *    - Automatic OAuth token refresh and storage
 *    - Marketplace and language header normalization
 *    - Structured logging for all requests and responses
 *    - Persistent response/error storage for analytics/debugging (Convex)
 *    - Special handling for 204 No Content responses and SDK quirks
 *    - Consistent error normalization and retry logic for token errors
 *
 *  Header formatting is critical for eBay APIs. See the "CRITICAL EBAY HEADER RULES"
 *  section below for details.
 *
 *  Helper glossary:
 *    - `EbayOAuth`: Lightweight wrapper for eBay OAuth flows.
 *    - `getEbayCredentials` / `getOrRefreshEbayAppCredentials`: Convex mutations for token storage.
 *    - `handleEbayApiError`: Detects token errors and triggers silent refresh/retry.
 *    - `storeEbayResponse`: Persists request/response for audit/debugging.
 *
 *  This file is plumbing only—no business logic should be added here.
 * -----------------------------------------------------------------------------
 */

/**
 * ⚠️ CRITICAL EBAY HEADER RULES ⚠️
 *
 * eBay APIs are extremely strict about locale formatting in headers vs. request bodies.
 * - Headers (e.g. Accept-Language, Content-Language) MUST use hyphens: 'en-US'
 * - Request body locale fields MUST use underscores: 'en_US'
 * - Never mix these formats or eBay will reject the request.
 * - Set headers in one place only: either at client creation or per API call.
 */

/**
 * Format a locale string for use in HTTP headers (e.g. 'en-US').
 * eBay requires hyphens in header locales.
 * @param locale - The locale string to format (e.g. 'en_US' or 'en-US')
 * @returns The locale formatted with hyphens (e.g. 'en-US')
 */
export function toHeaderFormat(locale: string | LocaleCode): string {
  return locale.includes('_') ? locale.replace(/_/g, '-') : locale;
}

/**
 * Represents a buyer's location for contextual pricing and availability.
 * Used with the Buy Browse API for accurate shipping/tax/availability.
 */
interface ContextualLocation {
  /** Two-letter country code (ISO 3166) */
  country: string;
  /** Optional postal/ZIP code for more precise results */
  zip?: string;
}

/**
 * Generic type for eBay SDK client constructors.
 * Allows factories to instantiate any eBay SDK client type.
 */
type EbayClientConstructor<T, O = unknown> = new (options: O) => T;

/**
 * Standard structure for eBay API error responses.
 * Used for consistent error handling, logging, and storage.
 */
interface EbayErrorResponse {
  message: string;
  statusCode: number;
  body?: {
    errors?: Array<{
      errorId: number;
      domain: string;
      category: string;
      message: string;
      longMessage?: string;
      parameters?: Array<{ name: string; value: string }>;
      inputRefIds?: string[];
      outputRefIds?: string[];
      subDomain?: string;
    }>;
  };
}

/**
 * Create an eBay API client with user-specific credentials.
 * Handles token refresh, logging, error normalization, and response storage.
 *
 * @template T - The eBay SDK client type
 * @template O - The options type for the SDK client
 * @param ClientClass - The eBay SDK client class to instantiate
 * @param appUserId - The user ID for token management
 * @param apiName - The eBay API namespace (e.g. 'sell/account')
 * @param initialToken - The initial access token (will be refreshed as needed)
 * @param clientOptions - Additional client configuration options
 * @returns A fully configured eBay API client with token management and logging
 */
function createEbayClient<T extends object, O extends { environment?: unknown; token: unknown }>(
  ClientClass: EbayClientConstructor<T, O>,
  appUserId: string,
  apiName: string,
  initialToken: string,
  clientOptions: Partial<O> = {},
): T {
  log.debug('Creating eBay client', {
    appUserId,
    apiName,
    appUrl: env.NEXT_PUBLIC_APP_URL,
  });

  // Create OAuth client for token management
  const oauth = EbayOAuth.create({
    redirectUri: env.NEXT_PUBLIC_APP_URL,
    ruName: env.EBAY_RUNAME,
  });

  // Set initial token state (forces refresh on first use)
  oauth.setToken({
    accessToken: initialToken,
    refreshToken: '',
    expiresAt: Date.now(),
    isInitialState: true,
  });

  // Map API names to their Fern environments
  const environments = {
    'sell/account': ebaySellAccountV1Environment.Production,
    'sell/metadata': ebaySellMetadataEnvironment.Production,
    'commerce/identity': ebayCommerceIdentityEnvironment.Production,
    'commerce/taxonomy': ebayCommerceTaxonomyEnvironment.Production,
    'commerce/catalog': ebayCommerceCatalogBetaEnvironment.Production,
    'commerce/media': ebayCommerceMediaBetaEnvironment.Production,
    'buy/browse': ebayBuyBrowseEnvironment.Production,
  };

  const environment = environments[apiName as keyof typeof environments];
  if (!environment) {
    throw new Error(`Unknown API: ${apiName}`);
  }

  // Create the SDK client with a token supplier and any extra options
  const client = new ClientClass({
    environment,
    token: async () => {
      try {
        // Get fresh credentials from server-side auth
        const credentials = await getEbayCredentials(appUserId);

        if (!credentials?.accessToken || !credentials.refreshToken) {
          throw new Error('No valid eBay credentials found');
        }

        // Update OAuth client with current credentials
        oauth.setToken({
          accessToken: credentials.accessToken,
          refreshToken: credentials.refreshToken,
          expiresAt: credentials.expiresAt,
        });

        // Get token (refresh if needed)
        return oauth.getToken();
      } catch (error) {
        log.error('Failed to get valid token', {
          error: error instanceof Error ? error.message : 'Unknown error',
          appUserId,
          apiName,
        });
        throw error;
      }
    },
    ...clientOptions,
  } as O);

  // Proxy all API calls for logging, error handling, and response storage
  return new Proxy(client, {
    get(target: T, service: string | symbol) {
      const value = Reflect.get(target, service);
      if (typeof value === 'object' && value !== null) {
        return new Proxy(value, {
          get(serviceTarget, method: string | symbol) {
            const originalMethod = Reflect.get(serviceTarget, method);
            if (typeof originalMethod === 'function') {
              return async (...args: unknown[]) => {
                const timestamp = Date.now();
                const endpoint = `/${apiName}/${String(service)}/${String(method)}`;

                // Log request details
                const requestOptions = args[1] as { headers?: EbayHeaders } | undefined;
                log.debug('Making eBay API request', {
                  endpoint,
                  method: getHttpMethod(String(method)),
                  requestData: args[0],
                  requestHeaders: requestOptions?.headers,
                });

                try {
                  try {
                    // Execute the SDK method
                    const response = await originalMethod.apply(serviceTarget, args);

                    // Check for warnings in successful responses
                    const warnings = response?.warnings || [];

                    // Log 204 No Content responses
                    if (response === undefined) {
                      log.info('eBay API 204 No Content response', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        status: 204,
                        statusText: 'No Content',
                        responseType: 'undefined',
                        message: 'This is a 204 No Content response',
                      });
                    }

                    // Store successful response
                    await storeEbayResponse({
                      userId: appUserId,
                      endpoint,
                      method: getHttpMethod(String(method)),
                      timestamp,
                      statusCode: response === undefined ? 204 : 200,
                      statusText: response === undefined ? 'No Content' : 'OK',
                      success: true,
                      data: response,
                      warnings: warnings.length > 0 ? warnings : undefined,
                      headers: {
                        contentType: response === undefined ? undefined : 'application/json',
                      },
                    });

                    // Log warnings if present
                    if (warnings.length > 0) {
                      log.warn('API call successful with warnings', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        warnings,
                        requestData: args[0],
                      });
                    } else {
                      log.debug('API call successful', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        success: true,
                        statusCode: response === undefined ? 204 : 200,
                      });
                    }
                    return response;
                  } catch (potentialValidationError) {
                    // Special handling for SDK's 204 validation error
                    if (
                      potentialValidationError instanceof Error &&
                      potentialValidationError.message.includes(
                        'Expected object. Received undefined',
                      )
                    ) {
                      const httpMethod = getHttpMethod(String(method));
                      log.info('Caught and handled 204 No Content validation error', {
                        endpoint,
                        method: httpMethod,
                        error: potentialValidationError.message,
                        handled: true,
                      });

                      // Store as a 204 successful response
                      await storeEbayResponse({
                        userId: appUserId,
                        endpoint,
                        method: httpMethod,
                        timestamp,
                        statusCode: 204,
                        statusText: 'No Content',
                        success: true,
                        data: undefined,
                        headers: {
                          contentType: undefined,
                        },
                      });

                      log.debug('API call successful (204 No Content)', {
                        endpoint,
                        method: httpMethod,
                        success: true,
                        statusCode: 204,
                      });

                      return undefined;
                    }
                    // Not a 204 case, rethrow for normal error handling
                    throw potentialValidationError;
                  }
                } catch (error) {
                  // If this is a structured eBay API error
                  if (
                    error &&
                    typeof error === 'object' &&
                    'statusCode' in error &&
                    'body' in error
                  ) {
                    const ebayError = error as EbayErrorResponse;

                    // Normalize error category for storage/logging
                    const normalizeCategory = (
                      category: string | undefined,
                    ): 'REQUEST' | 'APPLICATION' | 'BUSINESS' => {
                      if (!category) return 'REQUEST';
                      const upperCategory = category.toUpperCase();
                      if (
                        upperCategory === 'REQUEST' ||
                        upperCategory === 'APPLICATION' ||
                        upperCategory === 'BUSINESS'
                      ) {
                        return upperCategory as 'REQUEST' | 'APPLICATION' | 'BUSINESS';
                      }
                      if (
                        upperCategory === 'REQUEST ERROR' ||
                        upperCategory === 'REQUEST_ERROR' ||
                        upperCategory === 'VALIDATION'
                      ) {
                        return 'REQUEST';
                      }
                      if (
                        upperCategory === 'BUSINESS ERROR' ||
                        upperCategory === 'BUSINESS_ERROR'
                      ) {
                        return 'BUSINESS';
                      }
                      return 'APPLICATION';
                    };

                    const errors = ebayError.body?.errors?.map((e) => ({
                      errorId: e.errorId,
                      domain: e.domain,
                      category: normalizeCategory(e.category),
                      message: e.message,
                      longMessage: e.longMessage,
                      subDomain: e.subDomain,
                      parameters: e.parameters?.map((p) => ({
                        name: p.name,
                        value: p.value,
                      })),
                      inputRefIds: e.inputRefIds,
                      outputRefIds: e.outputRefIds,
                    }));

                    log.debug('Storing eBay error response', {
                      userId: appUserId,
                      endpoint,
                      method: getHttpMethod(String(method)),
                      timestamp,
                      statusCode: ebayError.statusCode,
                      firstError: errors?.[0],
                      remainingErrors: errors?.slice(1),
                    });

                    try {
                      // Store failed response with all errors
                      const result = await storeEbayResponse({
                        userId: appUserId,
                        endpoint,
                        method: getHttpMethod(String(method)),
                        timestamp,
                        statusCode: ebayError.statusCode,
                        statusText: ebayError.message,
                        success: false,
                        errors: errors,
                        warnings: undefined,
                        data: null,
                        headers: {
                          contentType: 'application/json',
                        },
                        requestData: args[0],
                      });

                      log.debug('Successfully stored eBay error response', { result });
                    } catch (storeError) {
                      log.error('Failed to store eBay error response', {
                        error:
                          storeError instanceof Error
                            ? {
                                message: storeError.message,
                                stack: storeError.stack,
                              }
                            : storeError,
                        originalError: {
                          message: ebayError.message,
                          statusCode: ebayError.statusCode,
                        },
                      });
                    }

                    log.error('eBay API error', {
                      endpoint,
                      method: getHttpMethod(String(method)),
                      error: {
                        message: ebayError.message,
                        statusCode: ebayError.statusCode,
                        body: ebayError.body,
                      },
                      requestData: args[0],
                    });
                  } else {
                    // Store unexpected error
                    await storeEbayResponse({
                      userId: appUserId,
                      endpoint,
                      method: getHttpMethod(String(method)),
                      timestamp,
                      statusCode: 500,
                      statusText: error instanceof Error ? error.message : String(error),
                      success: false,
                      data: null,
                    });

                    log.error('Unexpected error in API call', {
                      endpoint,
                      method: getHttpMethod(String(method)),
                      error: error instanceof Error ? error.message : String(error),
                    });
                  }
                  throw error;
                }
              };
            }
            return originalMethod;
          },
        });
      }
      return value;
    },
  });
}

/**
 * Create an eBay API client with application-level credentials.
 * Used for APIs that do not require user authorization (e.g. taxonomy, catalog).
 * Handles token refresh, logging, error normalization, and response storage.
 *
 * @template T - The eBay SDK client type
 * @template O - The options type for the SDK client
 * @param ClientClass - The eBay SDK client class to instantiate
 * @param apiName - The eBay API namespace (e.g. 'commerce/taxonomy')
 * @param initialToken - The initial access token (will be refreshed as needed)
 * @param clientOptions - Additional client configuration options
 * @returns A fully configured eBay API client with app-level authentication
 */
function createEbayAppClient<T extends object, O extends { environment?: unknown; token: unknown }>(
  ClientClass: EbayClientConstructor<T, O>,
  apiName: string,
  initialToken: string,
  clientOptions: Partial<O> = {},
): T {
  // Create OAuth client for token management
  const oauth = EbayOAuth.create({
    redirectUri: env.NEXT_PUBLIC_APP_URL,
    ruName: env.EBAY_RUNAME,
  });

  // Set initial token state (forces refresh on first use)
  oauth.setToken({
    accessToken: initialToken,
    refreshToken: '',
    expiresAt: Date.now(),
    isInitialState: true,
  });

  // Map API names to their Fern environments
  const environments = {
    'sell/account': ebaySellAccountV1Environment.Production,
    'sell/metadata': ebaySellMetadataEnvironment.Production,
    'commerce/identity': ebayCommerceIdentityEnvironment.Production,
    'commerce/taxonomy': ebayCommerceTaxonomyEnvironment.Production,
    'commerce/catalog': ebayCommerceCatalogBetaEnvironment.Production,
    'commerce/media': ebayCommerceMediaBetaEnvironment.Production,
    'buy/browse': ebayBuyBrowseEnvironment.Production,
  };

  const environment = environments[apiName as keyof typeof environments];
  if (!environment) {
    throw new Error(`Unknown API: ${apiName}`);
  }

  // Create the SDK client with a token supplier and any extra options
  const client = new ClientClass({
    environment,
    token: async () => {
      try {
        // Get fresh app credentials
        const credentials = await getOrRefreshEbayAppCredentials();

        if (!credentials?.accessToken) {
          throw new Error('No valid eBay app credentials found');
        }

        // Update OAuth client with current credentials
        oauth.setToken({
          accessToken: credentials.accessToken,
          refreshToken: '',
          expiresAt: credentials.expiresAt,
        });

        return credentials.accessToken;
      } catch (error) {
        log.error('Failed to get valid app token', {
          error: error instanceof Error ? error.message : 'Unknown error',
          apiName,
        });
        throw error;
      }
    },
    ...clientOptions,
  } as O);

  // Proxy all API calls for logging, error handling, and response storage
  return new Proxy(client, {
    get(target: T, service: string | symbol) {
      const value = Reflect.get(target, service);
      if (typeof value === 'object' && value !== null) {
        return new Proxy(value, {
          get(serviceTarget, method: string | symbol) {
            const originalMethod = Reflect.get(serviceTarget, method);
            if (typeof originalMethod === 'function') {
              return async (...args: unknown[]) => {
                const timestamp = Date.now();
                const endpoint = `/${apiName}/${String(service)}/${String(method)}`;

                // Log request details
                const requestOptions = args[1] as { headers?: EbayHeaders } | undefined;
                log.debug('Making eBay API request (app client)', {
                  endpoint,
                  method: getHttpMethod(String(method)),
                  requestData: args[0],
                  requestHeaders: requestOptions?.headers,
                });

                try {
                  try {
                    // Execute the SDK method
                    const response = await originalMethod.apply(serviceTarget, args);

                    // Check for warnings in successful responses
                    const warnings = response?.warnings || [];

                    // Log 204 No Content responses
                    if (response === undefined) {
                      log.info('eBay API 204 No Content response (app client)', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        status: 204,
                        statusText: 'No Content',
                        responseType: 'undefined',
                        message: 'This is a 204 No Content response',
                      });
                    }

                    // Store successful response
                    await storeEbayResponse({
                      userId: 'app',
                      endpoint,
                      method: getHttpMethod(String(method)),
                      timestamp,
                      statusCode: response === undefined ? 204 : 200,
                      statusText: response === undefined ? 'No Content' : 'OK',
                      success: true,
                      data: response,
                      warnings: warnings.length > 0 ? warnings : undefined,
                      headers: {
                        contentType: response === undefined ? undefined : 'application/json',
                      },
                    });

                    // Log warnings if present
                    if (warnings.length > 0) {
                      log.warn('API call successful with warnings (app client)', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        warnings,
                        requestData: args[0],
                      });
                    }
                    return response;
                  } catch (potentialValidationError) {
                    // Special handling for SDK's 204 validation error
                    if (
                      potentialValidationError instanceof Error &&
                      potentialValidationError.message.includes(
                        'Expected object. Received undefined',
                      )
                    ) {
                      const httpMethod = getHttpMethod(String(method));
                      log.info('Caught and handled 204 No Content validation error (app client)', {
                        endpoint,
                        method: httpMethod,
                        error: potentialValidationError.message,
                        handled: true,
                      });

                      // Store as a 204 successful response
                      await storeEbayResponse({
                        userId: 'app',
                        endpoint,
                        method: httpMethod,
                        timestamp,
                        statusCode: 204,
                        statusText: 'No Content',
                        success: true,
                        data: undefined,
                        headers: {
                          contentType: undefined,
                        },
                      });

                      log.debug('API call successful (204 No Content) (app client)', {
                        endpoint,
                        method: httpMethod,
                        success: true,
                        statusCode: 204,
                      });

                      return undefined;
                    }
                    // Not a 204 case, rethrow for normal error handling
                    throw potentialValidationError;
                  }
                } catch (error) {
                  // Extract error details for logging and storage
                  const errorResponse = error as EbayErrorResponse;
                  const statusCode = errorResponse.statusCode || 500;
                  const errorDetails = errorResponse.body?.errors?.[0];

                  log.error('eBay API error (app client)', {
                    endpoint,
                    method: getHttpMethod(String(method)),
                    statusCode,
                    errorId: errorDetails?.errorId,
                    domain: errorDetails?.domain,
                    category: errorDetails?.category,
                    message: errorDetails?.message,
                    longMessage: errorDetails?.longMessage,
                    requestData: args[0],
                  });

                  // Store error response for analytics
                  await storeEbayResponse({
                    userId: 'app',
                    endpoint,
                    method: getHttpMethod(String(method)),
                    timestamp,
                    statusCode,
                    statusText: errorResponse.message || 'Error',
                    success: false,
                    errors: [
                      errorDetails
                        ? {
                            errorId: errorDetails.errorId || 0,
                            domain: errorDetails.domain || 'API',
                            category: (errorDetails.category || 'APPLICATION') as
                              | 'REQUEST'
                              | 'APPLICATION'
                              | 'BUSINESS',
                            message:
                              errorDetails.message || errorResponse.message || 'Unknown error',
                            longMessage: errorDetails.longMessage,
                            subDomain: errorDetails.subDomain,
                            parameters: errorDetails.parameters,
                            inputRefIds: errorDetails.inputRefIds,
                            outputRefIds: errorDetails.outputRefIds,
                          }
                        : {
                            errorId: 0,
                            domain: 'API',
                            category: 'APPLICATION',
                            message: errorResponse.message || 'Unknown error',
                          },
                    ],
                    data: null,
                    headers: {
                      contentType: 'application/json',
                    },
                  });

                  // If this is an invalid token error, handle and retry once
                  const isHandled = await handleEbayApiError(
                    error,
                    `api:ebay:${apiName.replace('/', ':')}:${String(service)}:${String(method)}`,
                  );

                  if (isHandled) {
                    log.info('Retrying request after token refresh (app client)', {
                      endpoint,
                      method: getHttpMethod(String(method)),
                    });

                    try {
                      // Get a fresh token after the refresh
                      const credentials = await getOrRefreshEbayAppCredentials();

                      // Update OAuth client with refreshed credentials
                      oauth.setToken({
                        accessToken: credentials.accessToken,
                        refreshToken: '',
                        expiresAt: credentials.expiresAt,
                      });

                      // Retry the original method
                      const response = await originalMethod.apply(serviceTarget, args);

                      log.info('Retry successful after token refresh (app client)', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                      });

                      return response;
                    } catch (retryError) {
                      log.error('Retry failed after token refresh (app client)', {
                        endpoint,
                        method: getHttpMethod(String(method)),
                        error: retryError instanceof Error ? retryError.message : 'Unknown error',
                      });

                      throw error;
                    }
                  }

                  throw error;
                }
              };
            }
            return originalMethod;
          },
        });
      }
      return value;
    },
  });
}

/**
 * Map SDK method names to HTTP verbs for logging/analytics.
 * @param methodName - The SDK method name (e.g. 'getInventoryItem')
 * @returns The HTTP method (GET, POST, PUT, DELETE)
 */
function getHttpMethod(methodName: string): string {
  if (methodName.startsWith('get')) return 'GET';
  if (methodName.startsWith('create')) return 'POST';
  if (methodName.startsWith('update')) return 'PUT';
  if (methodName.startsWith('delete')) return 'DELETE';
  return 'POST';
}

/**
 * Create an eBay Sell Account API client.
 * Adds Accept-Language, Content-Language, and Marketplace headers to all requests.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @param options - Optional marketplace and language settings
 * @returns A Sell Account API client with proper header injection
 */
export function createSellAccountClient(
  appUserId: string,
  accessToken: string,
  options: {
    marketplaceId?: string;
    acceptLanguage?: string;
  } = {},
) {
  // Create the base client
  const client = createEbayClient<ebaySellAccountV1Client, ebaySellAccountV1Client['_options']>(
    ebaySellAccountV1Client,
    appUserId,
    'sell/account',
    accessToken,
  );

  // Proxy to inject headers for every service method
  return new Proxy(client, {
    get(target, prop) {
      const value = Reflect.get(target, prop);

      // If this is a service object (e.g. paymentPolicy, fulfillmentPolicy, etc.)
      if (typeof value === 'object' && value !== null) {
        return new Proxy(value, {
          get(serviceTarget, method) {
            const originalMethod = Reflect.get(serviceTarget, method);

            if (typeof originalMethod === 'function') {
              return function (...args: unknown[]) {
                // Prepare headers based on options
                const headers: EbayHeaders = {
                  'Accept-Language': options.acceptLanguage
                    ? toHeaderFormat(options.acceptLanguage)
                    : 'en-US',
                  'Content-Language': options.acceptLanguage
                    ? toHeaderFormat(options.acceptLanguage)
                    : 'en-US',
                };

                if (options.marketplaceId) {
                  headers['X-EBAY-C-MARKETPLACE-ID'] = options.marketplaceId;
                }

                // Add headers to request options
                if (args.length >= 2) {
                  const requestOptions = (args[1] as { headers?: EbayHeaders }) || {};
                  requestOptions.headers = { ...headers, ...(requestOptions.headers || {}) };
                  args[1] = requestOptions;
                } else if (args.length === 1) {
                  args.push({ headers });
                }

                // Debug log the request
                log.debug('Making eBay API call', {
                  service: String(prop),
                  method: String(method),
                  args: args.map((arg, index) =>
                    index === 0
                      ? arg
                      : index === 1 && typeof arg === 'object' && arg !== null
                        ? {
                            ...arg,
                            headers: Object.keys(
                              (arg as { headers?: Record<string, string> }).headers || {},
                            ),
                          }
                        : arg,
                  ),
                });

                return originalMethod.apply(serviceTarget, args);
              };
            }

            return originalMethod;
          },
        });
      }

      return value;
    },
  });
}

/**
 * Create an eBay Sell Metadata API client.
 * Used for retrieving marketplace-specific metadata (item conditions, return policies, etc).
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @returns A Sell Metadata API client
 */
export function createSellMetadataClient(appUserId: string, accessToken: string) {
  return createEbayClient<ebaySellMetadataClient, ebaySellMetadataClient['_options']>(
    ebaySellMetadataClient,
    appUserId,
    'sell/metadata',
    accessToken,
  );
}

/**
 * Create an eBay Commerce Identity API client.
 * Used for user profile and identity information.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @returns A Commerce Identity API client
 */
export function createIdentityClient(appUserId: string, accessToken: string) {
  return createEbayClient<ebayCommerceIdentityClient, ebayCommerceIdentityClient['_options']>(
    ebayCommerceIdentityClient,
    appUserId,
    'commerce/identity',
    accessToken,
  );
}

/**
 * Create an eBay Commerce Taxonomy API client.
 * Used for category information and suggested categories.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @returns A Commerce Taxonomy API client
 */
export function createTaxonomyClient(appUserId: string, accessToken: string) {
  return createEbayClient<ebayCommerceTaxonomyClient, ebayCommerceTaxonomyClient['_options']>(
    ebayCommerceTaxonomyClient,
    appUserId,
    'commerce/taxonomy',
    accessToken,
  );
}

/**
 * Create an eBay Commerce Catalog API client.
 * Used for product catalog information and product aspects.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @returns A Commerce Catalog API client
 */
export function createCatalogClient(appUserId: string, accessToken: string) {
  return createEbayClient<ebayCommerceCatalogBetaClient, ebayCommerceCatalogBetaClient['_options']>(
    ebayCommerceCatalogBetaClient,
    appUserId,
    'commerce/catalog',
    accessToken,
  );
}

/**
 * Create an eBay Commerce Media API client.
 * Used for uploading and managing product images.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @returns A Commerce Media API client
 */
export function createMediaClient(appUserId: string, accessToken: string) {
  return createEbayClient<ebayCommerceMediaBetaClient, ebayCommerceMediaBetaClient['_options']>(
    ebayCommerceMediaBetaClient,
    appUserId,
    'commerce/media',
    accessToken,
  );
}

/**
 * Create an eBay Buy Browse API client with marketplace, language, and location options.
 * Used for searching and browsing eBay listings.
 *
 * @param appUserId - The user ID for token management
 * @param accessToken - The initial access token (will be refreshed as needed)
 * @param options - Optional marketplace, language, and contextual location settings
 * @returns A Buy Browse API client
 */
export function createBuyBrowseClient(
  appUserId: string,
  accessToken: string,
  options: {
    marketplaceId?: string;
    acceptLanguage?: string;
    contextualLocation?: ContextualLocation;
  } = {},
) {
  // Build the X-EBAY-C-ENDUSERCTX header
  const endUserCtxParts: string[] = [];
  endUserCtxParts.push(`affiliateCampaignId=${env.EBAY_AFFILIATE_TRACKING_ID}`);
  if (options.contextualLocation) {
    const { country, zip } = options.contextualLocation;
    const locationParts = [`country=${country}`];
    if (zip) locationParts.push(`zip=${zip}`);
    endUserCtxParts.push(`contextualLocation=${encodeURIComponent(locationParts.join(','))}`);
  }

  return createEbayClient<ebayBuyBrowseClient, ebayBuyBrowseClient['_options']>(
    ebayBuyBrowseClient,
    appUserId,
    'buy/browse',
    accessToken,
    {
      ebayCEnduserctx: () => endUserCtxParts.join(','),
      ebayCMarketplaceId: () => options.marketplaceId,
      acceptLanguage: () =>
        options.acceptLanguage ? toHeaderFormat(options.acceptLanguage) : 'en-US',
    },
  );
}

/**
 * Create an eBay Buy Browse API client with app-level credentials.
 * Used for searching and browsing eBay listings as the application.
 *
 * @param initialToken - The initial app access token (optional)
 * @param options - Optional marketplace, language, and contextual location settings
 * @returns A Buy Browse API client (app context)
 */
export async function createAppBuyBrowseClient(
  initialToken: string = '',
  options: {
    marketplaceId?: string;
    acceptLanguage?: string;
    contextualLocation?: ContextualLocation;
  } = {},
) {
  // Build the X-EBAY-C-ENDUSERCTX header
  const endUserCtxParts: string[] = [];
  endUserCtxParts.push(`affiliateCampaignId=${env.EBAY_AFFILIATE_TRACKING_ID}`);
  if (options.contextualLocation) {
    const { country, zip } = options.contextualLocation;
    const locationParts = [`country=${country}`];
    if (zip) locationParts.push(`zip=${zip}`);
    endUserCtxParts.push(`contextualLocation=${encodeURIComponent(locationParts.join(','))}`);
  }

  // Get initial app token if not provided
  const token = initialToken || (await getInitialAppToken());

  return createEbayAppClient<ebayBuyBrowseClient, ebayBuyBrowseClient['_options']>(
    ebayBuyBrowseClient,
    'buy/browse',
    token,
    {
      ebayCEnduserctx: () => {
        const ctx = endUserCtxParts.join(',');
        log.debug('eBay Browse API - X-EBAY-C-ENDUSERCTX', { ctx });
        return ctx;
      },
      ebayCMarketplaceId: () => {
        log.debug('eBay Browse API - X-EBAY-C-MARKETPLACE-ID', { marketplaceId: options.marketplaceId });
        return options.marketplaceId;
      },
      acceptLanguage: () => {
        const lang = options.acceptLanguage ? toHeaderFormat(options.acceptLanguage) : 'en-US';
        log.debug('eBay Browse API - Accept-Language', { acceptLanguage: lang, originalInput: options.acceptLanguage });
        return lang;
      },
    },
  );
}

/**
 * Options for taxonomy client creation.
 */
interface TaxonomyClientOptions {
  marketplaceId?: string;
  acceptLanguage?: string;
}

/**
 * Create an eBay Commerce Taxonomy API client with app-level credentials.
 * Adds Accept-Language and Marketplace headers to all requests.
 *
 * @param initialToken - The initial app access token (optional)
 * @param options - Optional marketplace and language settings
 * @returns A Commerce Taxonomy API client (app context)
 */
export async function createAppTaxonomyClient(
  initialToken: string = '',
  options: TaxonomyClientOptions = {},
) {
  // Get initial app token if none provided
  if (!initialToken) {
    initialToken = await getInitialAppToken();
  }

  // Create the base client
  const client = createEbayAppClient<
    ebayCommerceTaxonomyClient,
    ebayCommerceTaxonomyClient['_options']
  >(ebayCommerceTaxonomyClient, 'commerce/taxonomy', initialToken, {});

  // Proxy to inject headers for every service method
  return new Proxy(client, {
    get(target, prop) {
      const value = Reflect.get(target, prop);
      if (typeof value === 'object' && value !== null) {
        return new Proxy(value, {
          get(serviceTarget, method) {
            const originalMethod = Reflect.get(serviceTarget, method);
            if (typeof originalMethod === 'function') {
              return function (...args: unknown[]) {
                // Add headers to request options
                const headers: Record<string, string> = {
                  'Accept-Language': options.acceptLanguage || 'en-US',
                };
                if (options.marketplaceId) {
                  headers['X-EBAY-C-MARKETPLACE-ID'] = options.marketplaceId;
                }
                if (args.length > 0) {
                  if (args.length > 1 && args[1]) {
                    const existingOptions = args[1] as { headers?: Record<string, string> };
                    existingOptions.headers = {
                      ...headers,
                      ...(existingOptions.headers || {}),
                    };
                  } else {
                    args.push({ headers });
                  }
                }
                return originalMethod.apply(serviceTarget, args);
              };
            }
            return originalMethod;
          },
        });
      }
      return value;
    },
  });
}

/**
 * Options for metadata client creation.
 */
interface MetadataClientOptions {
  marketplaceId?: string;
  acceptLanguage?: string;
}

/**
 * Create an eBay Sell Metadata API client with app-level credentials.
 * Adds Accept-Language and Marketplace headers to all requests.
 * Skips client creation during production build phase.
 *
 * @param initialToken - The initial app access token (optional)
 * @param options - Optional marketplace and language settings
 * @returns A Sell Metadata API client (app context) or null during build
 */
export async function createAppMetadataClient(
  initialToken: string = '',
  options: MetadataClientOptions = {},
) {
  // Skip client creation during production build
  if (
    process.env.NODE_ENV === 'production' &&
    process.env.NEXT_PHASE === 'phase-production-build'
  ) {
    log.info('Skipping metadata client creation during build');
    return null;
  }

  // Get initial app token if not provided
  const token = initialToken || (await getInitialAppToken());

  try {
    // Create the client with minimal options
    const client = createEbayAppClient<ebaySellMetadataClient, ebaySellMetadataClient['_options']>(
      ebaySellMetadataClient,
      'sell/metadata',
      token,
      {},
    );

    // Proxy to inject headers for every service method
    return new Proxy(client, {
      get(target, prop) {
        const value = Reflect.get(target, prop);

        // If this is a service object (e.g. "marketplace")
        if (typeof value === 'object' && value !== null) {
          return new Proxy(value, {
            get(serviceTarget, method) {
              const originalMethod = Reflect.get(serviceTarget, method);

              if (typeof originalMethod === 'function') {
                return function <TRequest, TOptions extends { headers?: Record<string, string> }>(
                  ...args: [TRequest?, TOptions?]
                ) {
                  // Add headers to request options if needed
                  const headers: Record<string, string> = {};

                  if (options.marketplaceId) {
                    headers['X-EBAY-C-MARKETPLACE-ID'] = options.marketplaceId;
                  }

                  if (options.acceptLanguage) {
                    headers['Accept-Language'] = toHeaderFormat(options.acceptLanguage);
                  }

                  if (args.length > 0) {
                    if (args.length > 1 && args[1]) {
                      const existingOptions = args[1] as TOptions;
                      existingOptions.headers = {
                        ...headers,
                        ...(existingOptions.headers || {}),
                      };
                    } else {
                      args.push({ headers } as unknown as TOptions);
                    }
                  }

                  return originalMethod.apply(serviceTarget, args);
                };
              }

              return originalMethod;
            },
          });
        }

        return value;
      },
    });
  } catch (error) {
    log.error('Error creating metadata client', { error, options });
    throw error;
  }
}

/**
 * Helper to get an initial app token for client creation.
 * Returns an empty string during build phase to avoid errors.
 * @returns The app access token, or empty string if unavailable
 */
async function getInitialAppToken(): Promise<string> {
  try {
    // Skip during build time to prevent errors
    if (
      process.env.NODE_ENV === 'production' &&
      process.env.NEXT_PHASE === 'phase-production-build'
    ) {
      log.info('Skipping app token retrieval during build');
      return '';
    }

    const credentials = await getOrRefreshEbayAppCredentials();

    if (!credentials?.accessToken) {
      log.warn('No valid app token found, returning empty string');
      return '';
    }

    return credentials.accessToken;
  } catch (error) {
    log.error('Failed to get initial app token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    // Return empty string instead of throwing to allow client creation to proceed
    // The token function will attempt to get a fresh token when needed
    return '';
  }
}
