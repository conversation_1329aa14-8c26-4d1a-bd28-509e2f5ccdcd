/**
 * eBay Region Types and Marketplace Definitions
 * Based on: https://developer.ebay.com/api-docs/buy/static/ref-buy-browse-filters.html#Valid
 */

// Import the authoritative country-to-marketplace mapping
import { COUNTRY_TO_MARKETPLACE as COUNTRY_MARKETPLACE_MAP, getMarketplaceForCountry as getMarketplaceForCountryImpl } from '@/lib/ebay/utils/country-marketplace-mapping';

// All possible eBay regions
export type EbayRegion =
    | 'NORTH_AMERICA'
    | 'EUROPEAN_UNION'
    | 'CONTINENTAL_EUROPE'
    | 'BORDER_COUNTRIES'
    | 'UK_AND_IRELAND'
    | 'ASIA'
    | 'WORLDWIDE';

// Runtime array for validation (must match the type above)
export const VALID_EBAY_REGIONS: readonly EbayRegion[] = [
    'NORTH_AMERICA',
    'EUROPEAN_UNION',
    'CONTINENTAL_EUROPE',
    'BORDER_COUNTRIES',
    'UK_AND_IRELAND',
    'ASIA',
    'WORLDWIDE'
] as const;

/**
 * Type guard to check if a string is a valid EbayRegion
 */
export function isValidEbayRegion(value: string): value is EbayRegion {
    return (VALID_EBAY_REGIONS as readonly string[]).includes(value);
}

// eBay marketplaces
export type EbayMarketplace =
    | 'EBAY_US' | 'EBAY_GB' | 'EBAY_DE' | 'EBAY_CA' | 'EBAY_AU'
    | 'EBAY_FR' | 'EBAY_IT' | 'EBAY_ES' | 'EBAY_BE' | 'EBAY_AT'
    | 'EBAY_CH' | 'EBAY_NL' | 'EBAY_PL' | 'EBAY_IE' | 'EBAY_SG' | 'EBAY_HK';

// Location filter - direct eBay API structure
export interface LocationFilter {
    type: 'country' | 'region';
    value: string; // Country code (e.g., 'US') or region (e.g., 'EUROPEAN_UNION')
    displayName: string;
}

// Marketplace to valid regions mapping (from eBay docs)
export const MARKETPLACE_REGIONS: Record<EbayMarketplace, EbayRegion[]> = {
    EBAY_US: ['NORTH_AMERICA', 'WORLDWIDE'],
    EBAY_GB: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_DE: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_CA: ['NORTH_AMERICA', 'WORLDWIDE'],
    EBAY_AU: ['WORLDWIDE'], // Only WORLDWIDE!
    EBAY_FR: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'BORDER_COUNTRIES', 'WORLDWIDE'],
    EBAY_IT: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_ES: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_BE: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_AT: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_CH: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_NL: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_PL: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'WORLDWIDE'],
    EBAY_IE: ['EUROPEAN_UNION', 'CONTINENTAL_EUROPE', 'UK_AND_IRELAND', 'WORLDWIDE'],
    EBAY_SG: ['ASIA', 'WORLDWIDE'],
    EBAY_HK: ['ASIA', 'BORDER_COUNTRIES', 'WORLDWIDE'],
};

// Re-export for compatibility (regions.ts uses EbayMarketplace type)
export const COUNTRY_TO_MARKETPLACE = COUNTRY_MARKETPLACE_MAP as Record<string, EbayMarketplace>;

// Country display names
export const COUNTRY_DISPLAY_NAMES: Record<string, string> = {
    'US': 'United States',
    'GB': 'United Kingdom',
    'UK': 'United Kingdom',
    'DE': 'Germany',
    'CA': 'Canada',
    'AU': 'Australia',
    'FR': 'France',
    'IT': 'Italy',
    'ES': 'Spain',
    'BE': 'Belgium',
    'AT': 'Austria',
    'CH': 'Switzerland',
    'NL': 'Netherlands',
    'PL': 'Poland',
    'IE': 'Ireland',
    'SG': 'Singapore',
    'HK': 'Hong Kong',
};

// Marketplace display names
export const MARKETPLACE_DISPLAY_NAMES: Record<EbayMarketplace, string> = {
    EBAY_US: 'United States',
    EBAY_GB: 'United Kingdom',
    EBAY_DE: 'Germany',
    EBAY_CA: 'Canada',
    EBAY_AU: 'Australia',
    EBAY_FR: 'France',
    EBAY_IT: 'Italy',
    EBAY_ES: 'Spain',
    EBAY_BE: 'Belgium',
    EBAY_AT: 'Austria',
    EBAY_CH: 'Switzerland',
    EBAY_NL: 'Netherlands',
    EBAY_PL: 'Poland',
    EBAY_IE: 'Ireland',
    EBAY_SG: 'Singapore',
    EBAY_HK: 'Hong Kong',
};

// Default regional preferences by marketplace (first non-WORLDWIDE region)
export const DEFAULT_REGIONAL_PREFERENCE: Record<EbayMarketplace, EbayRegion> = {
    EBAY_US: 'NORTH_AMERICA',
    EBAY_GB: 'EUROPEAN_UNION',
    EBAY_DE: 'EUROPEAN_UNION',
    EBAY_CA: 'NORTH_AMERICA',
    EBAY_AU: 'WORLDWIDE', // Only option
    EBAY_FR: 'EUROPEAN_UNION',
    EBAY_IT: 'EUROPEAN_UNION',
    EBAY_ES: 'EUROPEAN_UNION',
    EBAY_BE: 'EUROPEAN_UNION',
    EBAY_AT: 'EUROPEAN_UNION',
    EBAY_CH: 'EUROPEAN_UNION',
    EBAY_NL: 'EUROPEAN_UNION',
    EBAY_PL: 'EUROPEAN_UNION',
    EBAY_IE: 'EUROPEAN_UNION',
    EBAY_SG: 'ASIA',
    EBAY_HK: 'ASIA',
};

/**
 * Get valid regions for a country's primary marketplace
 */
export function getValidRegionsForCountry(countryCode: string): EbayRegion[] {
    const marketplace = COUNTRY_TO_MARKETPLACE[countryCode.toUpperCase()];
    if (!marketplace) {
        // Fallback for unsupported countries - they can only use WORLDWIDE
        return ['WORLDWIDE'];
    }
    return MARKETPLACE_REGIONS[marketplace];
}

/**
 * Get valid regions for a specific marketplace
 */
export function getValidRegionsForMarketplace(marketplace: EbayMarketplace): EbayRegion[] {
    return MARKETPLACE_REGIONS[marketplace];
}

/**
 * Get default regional preference for a country
 */
export function getDefaultRegionForCountry(countryCode: string): EbayRegion {
    const marketplace = COUNTRY_TO_MARKETPLACE[countryCode.toUpperCase()];
    if (!marketplace) {
        return 'WORLDWIDE';
    }
    return DEFAULT_REGIONAL_PREFERENCE[marketplace];
}

/**
 * Get marketplace for a country (re-exported for compatibility)
 */
export function getMarketplaceForCountry(countryCode: string): EbayMarketplace | null {
    return getMarketplaceForCountryImpl(countryCode) as EbayMarketplace | null;
}

/**
 * Get all available location options for a user's marketplace
 */
export function getAvailableLocationOptions(userCountry: string | null): LocationFilter[] {
    const options: LocationFilter[] = [];

    if (userCountry) {
        const marketplace = getMarketplaceForCountry(userCountry);

        // Add the user's country as an option (for "local" searches)
        options.push({
            type: 'country',
            value: userCountry,
            displayName: COUNTRY_DISPLAY_NAMES[userCountry] || userCountry
        });

        // Add all valid regions for this marketplace
        if (marketplace) {
            const validRegions = getValidRegionsForMarketplace(marketplace);
            validRegions.forEach(region => {
                options.push({
                    type: 'region',
                    value: region,
                    displayName: REGION_DISPLAY_NAMES[region]
                });
            });
        } else {
            // For unsupported countries (like JP), always add Worldwide as fallback
            options.push({
                type: 'region',
                value: 'WORLDWIDE',
                displayName: REGION_DISPLAY_NAMES['WORLDWIDE']
            });
        }
    } else {
        // Fallback if no country detected - just worldwide
        options.push({
            type: 'region',
            value: 'WORLDWIDE',
            displayName: REGION_DISPLAY_NAMES['WORLDWIDE']
        });
    }

    return options;
}

/**
 * Convert location filter to actual eBay filter values
 */
export function locationFilterToEbayFilters(
    locationFilter: LocationFilter
): { itemLocationCountry?: string; itemLocationRegion?: EbayRegion } {
    if (locationFilter.type === 'country') {
        return { itemLocationCountry: locationFilter.value };
    } else {
        return { itemLocationRegion: locationFilter.value as EbayRegion };
    }
}

/**
 * Detect current location filter from applied filters
 */
export function detectCurrentLocationFilter(
    itemLocationCountry?: string,
    itemLocationRegion?: string): LocationFilter | null {
    // Check for country filter
    if (itemLocationCountry) {
        return {
            type: 'country',
            value: itemLocationCountry,
            displayName: COUNTRY_DISPLAY_NAMES[itemLocationCountry] || itemLocationCountry
        };
    }

    // Check for region filter
    if (itemLocationRegion) {
        const region = itemLocationRegion as EbayRegion;
        return {
            type: 'region',
            value: region,
            displayName: REGION_DISPLAY_NAMES[region]
        };
    }

    // No location filters = null (no specific filter selected)
    return null;
}

/**
 * Get default location filter for a user (their country)
 */
export function getDefaultLocationFilter(userCountry: string | null): LocationFilter {
    if (userCountry && COUNTRY_DISPLAY_NAMES[userCountry]) {
        return {
            type: 'country',
            value: userCountry,
            displayName: COUNTRY_DISPLAY_NAMES[userCountry]
        };
    }

    // Fallback to worldwide if no country
    return {
        type: 'region',
        value: 'WORLDWIDE',
        displayName: REGION_DISPLAY_NAMES['WORLDWIDE']
    };
}

/**
 * Region display names for UI
 */
export const REGION_DISPLAY_NAMES: Record<EbayRegion, string> = {
    'NORTH_AMERICA': 'North America',
    'EUROPEAN_UNION': 'European Union',
    'CONTINENTAL_EUROPE': 'Continental Europe',
    'BORDER_COUNTRIES': 'Border Countries',
    'UK_AND_IRELAND': 'UK & Ireland',
    'ASIA': 'Asia',
    'WORLDWIDE': 'Worldwide',
}; 