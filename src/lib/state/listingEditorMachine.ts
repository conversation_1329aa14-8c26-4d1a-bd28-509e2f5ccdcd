import { createMachine, assign, createActor } from 'xstate';
import { logger } from '@/lib/logger';
import {
    FetchStatus,
    OperationStatus,
    ListingEditorStates,
    type FetchStatusType,
    type OperationStatusType,
} from './listingMachineStates';

const log = logger.create('xstate:listingEditor');

// Use centralized status types from listingMachineStates
export type { FetchStatusType as FetchStatus, OperationStatusType as OperationStatus };

/**
 * Tracks fetch status for category metadata (conditions, aspects) by category.
 */
export interface CategoryMetadataStatus {
    conditions: FetchStatusType;
    aspects: FetchStatusType;
    lastError?: string;
}

/**
 * Context for the listing editor XState machine.
 * Holds all operation and fetch statuses, plus error info.
 */
export interface ListingEditorContext {
    /**
     * Tracks metadata fetch status for each categoryId.
     * Example: { "123": { conditions: "loaded", aspects: "loading" } }
     */
    statusByCategoryId: Record<string, Partial<CategoryMetadataStatus>>;

    // Save draft operation status and last error
    saveDraftStatus: OperationStatusType;
    lastSaveError?: string;

    // Verify listing operation status and last error
    verifyListingStatus: OperationStatusType;
    lastVerifyError?: string;

    // Publish listing operation status and last error
    publishListingStatus: OperationStatusType;
    lastPublishError?: string;

    // AI generate operation status and last error
    aiGenerateStatus: OperationStatusType;
    lastAIGenerateError?: string;
}

/**
 * Events handled by the listing editor machine.
 * Each event updates a specific status or error in the context.
 */
export type ListingEditorEvent =
    | {
        type: 'SET_CATEGORY_STATUS';
        categoryId: string;
        metadataType: keyof Omit<CategoryMetadataStatus, 'lastError'>;
        status: FetchStatusType;
        error?: string;
    }
    | { type: 'SET_SAVE_STATUS'; status: OperationStatusType; error?: string; }
    | { type: 'SET_VERIFY_STATUS'; status: OperationStatusType; error?: string; }
    | { type: 'SET_PUBLISH_STATUS'; status: OperationStatusType; error?: string; }
    | { type: 'SET_AI_GENERATE_STATUS'; status: OperationStatusType; error?: string; };

/**
 * XState v5 machine for listing editor state.
 * All status and error updates are handled via events.
 */
export const listingEditorMachine = createMachine({
    id: 'listingEditor',
    types: {
        context: {} as ListingEditorContext,
        events: {} as ListingEditorEvent,
    },

    context: {
        statusByCategoryId: {},
        saveDraftStatus: OperationStatus.IDLE,
        lastSaveError: undefined,
        verifyListingStatus: OperationStatus.IDLE,
        lastVerifyError: undefined,
        publishListingStatus: OperationStatus.IDLE,
        lastPublishError: undefined,
        aiGenerateStatus: OperationStatus.IDLE,
        lastAIGenerateError: undefined,
    },

    initial: ListingEditorStates.READY,

    states: {
        [ListingEditorStates.READY]: {
            on: {
                // Update category metadata fetch status for a given categoryId/type
                SET_CATEGORY_STATUS: {
                    actions: assign({
                        statusByCategoryId: ({ context, event }) => {
                            const { categoryId, metadataType, status, error } = event;
                            const currentCategoryStatus = context.statusByCategoryId[categoryId] || {};

                            // Skip update if status is unchanged
                            if (currentCategoryStatus[metadataType] === status) {
                                return context.statusByCategoryId;
                            }

                            const newStatus = {
                                ...currentCategoryStatus,
                                [metadataType]: status,
                                lastError: status === FetchStatus.ERROR ? error || 'Unknown error' : currentCategoryStatus.lastError,
                            };

                            log.debug('Updating metadata status', { categoryId, metadataType, status, error });

                            return {
                                ...context.statusByCategoryId,
                                [categoryId]: newStatus,
                            };
                        }
                    })
                },

                // Update save draft operation status and error
                SET_SAVE_STATUS: {
                    actions: assign({
                        saveDraftStatus: ({ event }) => event.status,
                        lastSaveError: ({ context, event }) => {
                            if (context.saveDraftStatus === event.status && context.lastSaveError === event.error) {
                                return context.lastSaveError;
                            }
                            log.debug('Updating save status', { status: event.status, error: event.error });
                            return event.status === OperationStatus.ERROR ? event.error : undefined;
                        }
                    })
                },

                // Update verify operation status and error
                SET_VERIFY_STATUS: {
                    actions: assign({
                        verifyListingStatus: ({ event }) => event.status,
                        lastVerifyError: ({ context, event }) => {
                            if (context.verifyListingStatus === event.status && context.lastVerifyError === event.error) {
                                return context.lastVerifyError;
                            }
                            log.debug('Updating verify status', { status: event.status, error: event.error });
                            return event.status === OperationStatus.ERROR ? event.error : undefined;
                        }
                    })
                },

                // Update publish operation status and error
                SET_PUBLISH_STATUS: {
                    actions: assign({
                        publishListingStatus: ({ event }) => event.status,
                        lastPublishError: ({ context, event }) => {
                            if (context.publishListingStatus === event.status && context.lastPublishError === event.error) {
                                return context.lastPublishError;
                            }
                            log.debug('Updating publish status', { status: event.status, error: event.error });
                            return event.status === OperationStatus.ERROR ? event.error : undefined;
                        }
                    })
                },

                // Update AI generate operation status and error
                SET_AI_GENERATE_STATUS: {
                    actions: assign({
                        aiGenerateStatus: ({ event }) => event.status,
                        lastAIGenerateError: ({ context, event }) => {
                            if (context.aiGenerateStatus === event.status && context.lastAIGenerateError === event.error) {
                                return context.lastAIGenerateError;
                            }
                            log.debug('Updating AI generate status', { status: event.status, error: event.error });
                            return event.status === OperationStatus.ERROR ? event.error : undefined;
                        }
                    })
                }
            }
        }
    }
});

/**
 * Singleton actor instance for the listing editor machine.
 * XState v5 pattern: create and start immediately.
 */
export const listingEditorActor = createActor(listingEditorMachine).start();

/**
 * Helper to get the fetch status for a given category and metadata type.
 * Returns 'idle' if categoryId is missing or no status is found.
 */
export function getCategoryStatus(
    context: ListingEditorContext,
    categoryId: string | undefined | null,
    metadataType: keyof Omit<CategoryMetadataStatus, 'lastError'>
): FetchStatusType {
    if (!categoryId) {
        return FetchStatus.IDLE;
    }
    const status = context.statusByCategoryId[categoryId]?.[metadataType];
    return status ?? FetchStatus.IDLE;
}

// Types are already exported above via the centralized imports 