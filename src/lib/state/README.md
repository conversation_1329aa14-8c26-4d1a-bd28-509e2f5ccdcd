# XState v5 Listing State Management

This directory contains the XState v5 implementation for eBay listing state management, replacing the previous pure function approach with a reactive state machine.

## 🚀 Migration Complete

The migration from pure functions to XState v5 is **complete and production-ready**. All tests pass and the implementation follows XState v5 best practices.

## 📁 Key Files

- **`listingStateMachine.ts`** - Core XState v5 machine with state logic
- **`useListingStateMachine.ts`** - React hooks for safe per-component usage
- **`../ebay/state-management.ts`** - Re-exports for backward compatibility

## 🔧 Usage Patterns

### ✅ Recommended: Per-Component State Machine

```typescript
import { useListingStateMachine } from '@/lib/state/useListingStateMachine';

function ListingComponent({ listingId }: { listingId: string }) {
  // Each component gets its own actor - no state collisions!
  const { state, actions } = useListingStateMachine({
    timestamps: listing?.timestamps || null
  });

  const handleEdit = () => {
    actions.localEdit('new-checksum-123');
  };

  return (
    <div>
      <p>Status: {state.currentState}</p>
      <button onClick={handleEdit} disabled={!state.canEdit}>
        Edit
      </button>
    </div>
  );
}
```

### ✅ Backward Compatibility (Pure Functions)

```typescript
import { 
  determineEntityStateReactive,
  getEntityStateLabelReactive 
} from '@/lib/state/useListingStateMachine';

// These work exactly like the old functions
const state = determineEntityStateReactive(timestamps);
const label = getEntityStateLabelReactive(state);
```

### ❌ Avoid: Global State (Fixed)

The dangerous global actor pattern has been **removed**. All hooks now create per-component actors with automatic cleanup.

## 🧪 Testing

All XState machines use comprehensive test utilities in `__tests__/xstate-test-utils.ts`:

```typescript
import { createTestActor, testStateTransition } from './xstate-test-utils';

// Subscription-based waiting (not polling)
await testStateTransition(actor, {
  initialState: 'draft',
  event: { type: 'START_PUSH' },
  expectedFinalState: 'pushing'
});
```

## 🔄 State Flow

```
DRAFT → (START_PUSH) → PUSHING → (PUSH_SUCCESS) → PENDING
  ↑                                                   ↓
  ← (LOCAL_EDIT) ← ACTIVE ← (SYNC_SUCCESS) ←---------┘
                     ↓
                  (MARK_ENDED)
                     ↓
                   ENDED
```

## 🛡️ Safety Features

1. **Memory Leak Prevention** - All actors auto-cleanup on unmount
2. **No State Collisions** - Per-component actors prevent cross-contamination
3. **SSR Safe** - No global singletons that leak across requests
4. **Type Safe** - Full TypeScript support with XState v5

## 📊 XState v5 Compliance

- ✅ **XState v5.19.4** + **@xstate/react v5.0.5**
- ✅ **TypeScript v5.8.3** (meets v5 requirement)
- ✅ Uses `createActor()` instead of deprecated `interpret()`
- ✅ Uses `useSelector()` from `@xstate/react`
- ✅ Proper actor lifecycle management
- ✅ Subscription-based testing (no polling)

## 🔧 Migration Notes

The migration maintains **100% backward compatibility** through re-exports in `state-management.ts`. Existing code continues to work while new code can adopt the reactive patterns.

### Before (Pure Functions)
```typescript
import { determineEntityState } from '@/lib/ebay/state-management';
const state = determineEntityState(timestamps); // Still works!
```

### After (Reactive Machine)
```typescript
import { useListingStateMachine } from '@/lib/state/useListingStateMachine';
const { state, actions } = useListingStateMachine({ timestamps });
```

## 🚨 Critical Fixes Applied

1. **Removed Global Actor** - Eliminated dangerous singleton pattern
2. **Simplified Logic** - Removed duplicate checksum calculations
3. **Single Source of Truth** - All functions re-exported from machine
4. **Memory Management** - Proper actor cleanup on unmount

The implementation is now **production-ready** and follows all XState v5 best practices! 🎉 

# eBay Search Machine Architecture

This document describes the XState-based search architecture that powers the eBay search functionality. The system is built using a hierarchical state machine pattern with multiple coordinated machines handling different aspects of the search experience.

## 🏗️ Architecture Overview

The search system consists of four interconnected state machines that work together to provide a seamless search experience:

```mermaid
graph TB
    SR[SearchRootMachine] --> FM[FilterMachine]
    SR --> GM[GeoMachine] 
    SR --> USM[UrlSyncMachine]
    
    SR --> AI[AI Search Service]
    SR --> API[eBay API Service]
    
    FM --> URL[URL Updates]
    GM --> URL
    USM --> URL
    
    subgraph "External Dependencies"
        Router[Next.js Router]
        Cache[Query Cache]
        Storage[Local Storage]
        eBayAPI[eBay REST API]
        OpenAI[OpenAI API]
    end
    
    SR --> Router
    FM --> Cache
    GM --> Storage
    API --> eBayAPI
    AI --> OpenAI
```

## 🎯 Core Machines

### 1. SearchRootMachine (`searchRootMachine.ts`)

**Role**: Main orchestrator and coordinator for the entire search experience.

**Key Responsibilities**:
- Coordinates search execution and result management
- Manages AI-powered search processing
- Handles infinite scroll and pagination
- Orchestrates communication between child machines
- Manages search history and undo/redo functionality

**Key States**:
- `idle` - Ready for new search input
- `searching` - Actively querying eBay API
- `aiProcessing` - AI analyzing and refining query
- `searchComplete` - Results displayed, ready for interaction
- `loadingMore` - Fetching additional results (infinite scroll)
- `error` - Error state with user recovery options

**Context Structure**:
```typescript
interface SearchRootContext {
  // Query Management
  currentQuery: string;
  originalQuery: string;
  currentPage: number;
  
  // Results & Pagination
  results: SearchResults | null;
  allResults: EbayItemSummary[];
  totalResults: number | null;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  
  // Child Machine References
  geoRef?: { context: GeoContext };
  filterRef?: { context: FilterContext };
  urlSyncRef?: { context: UrlSyncContext };
  
  // Search History & Undo
  searchHistory: SearchHistoryItem[];
  _aiUndoStack?: UndoStep[];
  _aiRedoStack?: UndoStep[];
  
  // UI State
  error: Error | string | null;
  loadingMessage: string;
  initialLoadComplete: boolean;
  
  // Dependencies
  router?: Router;
  suppressAutoTrigger: boolean;
}
```

### 2. FilterMachine (`filterMachine.ts`)

**Role**: Manages search filters, business logic, and filter coordination.

**Key Responsibilities**:
- Handles all filter operations (add, remove, modify)
- Applies business rules (e.g., location changes clear pickup filters)
- Coordinates complex filter interactions
- Synchronizes filter state with URL and cache

**Key States**:
- `ready` - Default state, accepts filter operations
- `coordinating` - Processing complex filter updates with business logic
- `updatingUrlAndCache` - Synchronizing changes with URL and invalidating cache

**Filter Business Logic**:
- Location filter changes automatically clear pickup filters
- Condition filters are mutually exclusive (NEW, USED, or ALL)
- Buying options can be combined (AUCTION, FIXED_PRICE)
- Near Me requires user location data

**Context Structure**:
```typescript
interface FilterContext {
  filterString: string;      // Current filter state as eBay filter string
  sessionId: string;         // Unique session identifier
  router?: Router;           // Next.js router for URL updates
  queryClient?: QueryClient; // Cache invalidation
}
```

### 3. GeoMachine (`geoMachine.ts`)

**Role**: Handles geographic location detection and country selection.

**Key Responsibilities**:
- Auto-detects user location via geolocation API
- Manages manual country selection
- Processes URL-based country parameters
- Provides location context for filters and searches

**Key States**:
- `initializing` - Machine startup and context setup
- `resolvingFromUrl` - Checking URL for manual country selection
- `autoDetecting` - Using geolocation API for location detection
- `ready` - Location determined, available for use

**Location Resolution Priority**:
1. Manual selection from URL (`?from=US`)
2. Auto-detection via geolocation API
3. Default fallback (US)

**Context Structure**:
```typescript
interface GeoContext {
  country: string | null;
  postalCode: string | null;
  isManual: boolean;
  defaultLocationFilter: LocationFilter | null;
  hasInitialized: boolean;
}
```

### 4. UrlSyncMachine (`urlSyncMachine.ts`)

**Role**: Synchronizes application state with browser URL.

**Key Responsibilities**:
- Updates URL parameters when state changes
- Maintains shareable URLs for search states
- Handles browser navigation (back/forward)
- Preserves important parameters during updates

**Key States**:
- `idle` - Accumulating parameter updates
- `updating` - Actively updating browser URL

**URL Parameters Managed**:
- `q` - Search query
- `filter` - eBay filter string
- `page` - Current page (for pagination)
- `from` - Manual country selection

**Context Structure**:
```typescript
interface UrlSyncContext {
  currentUrl: string;
  pendingUpdates: Record<string, string | null>;
  isUpdating: boolean;
  router?: Router;
}
```

## 🔄 Machine Communication Patterns

### 1. Parent-Child Communication

The SearchRootMachine spawns child machines and communicates with them via:

```typescript
// Spawning child machines
spawn(geoMachine, { id: 'geo' })
spawn(filterMachine, { id: 'filter' })
spawn(urlSyncMachine, { id: 'urlSync' })

// Sending events to children
sendTo('filter', { type: 'ADD_FILTER', filterObject: {...} })
sendTo('geo', { type: 'DETECT' })
sendTo('urlSync', { type: 'UPDATE_QUERY', query: 'cameras' })
```

### 2. Event Forwarding

SearchRootMachine forwards events to appropriate child machines:

```typescript
// Filter-related events forwarded to FilterMachine
'filters.ADD_FILTER': { target: '.searchComplete', actions: forwardTo('filter') }
'filters.SET_CONDITION': { target: '.searchComplete', actions: forwardTo('filter') }

// Geo-related events forwarded to GeoMachine  
'geo.SET_COUNTRY': { target: '.searchComplete', actions: forwardTo('geo') }
'geo.DETECT': { target: '.searchComplete', actions: forwardTo('geo') }
```

### 3. State Coordination

The root machine coordinates state between children:

```typescript
// Derive location filter from geo + filter contexts
function deriveCurrentLocationFilter(
  geoContext: GeoContext | undefined,
  filterContext: FilterContext | undefined,
  urlParams?: URLSearchParams
): LocationFilter | null {
  // Complex logic to coordinate between machines
}
```

## 🚀 Search Flow Examples

### 1. Basic Search Flow

```mermaid
sequenceDiagram
    participant User
    participant SearchRoot
    participant eBayAPI
    participant UrlSync
    
    User->>SearchRoot: SEARCH {query: "cameras"}
    SearchRoot->>UrlSync: UPDATE_QUERY
    SearchRoot->>eBayAPI: searchEbayItemsViaRoute
    SearchRoot->>SearchRoot: state: searching
    eBayAPI-->>SearchRoot: results
    SearchRoot->>SearchRoot: state: searchComplete
    UrlSync->>Browser: Update URL with ?q=cameras
```

### 2. AI-Enhanced Search Flow

```mermaid
sequenceDiagram
    participant User
    participant SearchRoot
    participant AI
    participant Filter
    participant eBayAPI
    
    User->>SearchRoot: AI_SEARCH {query: "vintage film cameras"}
    SearchRoot->>SearchRoot: state: aiProcessing
    SearchRoot->>AI: Process natural language query
    AI-->>SearchRoot: {processedQuery: "film cameras", filters: {...}}
    SearchRoot->>Filter: MERGE_AI_FILTERS
    SearchRoot->>eBayAPI: Search with refined query & filters
    eBayAPI-->>SearchRoot: Enhanced results
    SearchRoot->>SearchRoot: state: searchComplete
```

### 3. Filter Coordination Flow

```mermaid
sequenceDiagram
    participant User
    participant SearchRoot
    participant Filter
    participant Geo
    participant UrlSync
    
    User->>SearchRoot: Change location filter
    SearchRoot->>Filter: UPDATE_FILTERS_WITH_COORDINATION
    Filter->>Filter: state: coordinating
    Filter->>Filter: Apply business rules (clear pickup)
    Filter->>Filter: state: updatingUrlAndCache
    Filter->>UrlSync: Update filter parameter
    Filter->>QueryCache: Invalidate search queries
    Filter->>SearchRoot: Coordination complete
    SearchRoot->>eBayAPI: New search with updated filters
```

## 🎛️ Advanced Features

### 1. Undo/Redo System

The SearchRootMachine maintains stacks for AI search refinements:

```typescript
interface UndoStep {
  currentQuery: string;
  originalQuery: string;
  filterString: string;
  resultCount?: number;
  timestamp: number;
  stepType: 'ai_refinement' | 'user_filter' | 'initial';
}

// Context includes:
_aiUndoStack?: UndoStep[];
_aiRedoStack?: UndoStep[];
```

**Usage**:
- Each AI refinement pushes current state to undo stack
- User can undo to previous search states
- Redo is available after undo operations
- Stack is cleared on new search sessions

### 2. Search History Management

Direct history management without complex state machine:

```typescript
const historyStorage = {
  load(): SearchHistoryItem[]
  save(items: SearchHistoryItem[]): void
  add(item: SearchHistoryItem, currentItems: SearchHistoryItem[]): SearchHistoryItem[]
  remove(id: string, currentItems: SearchHistoryItem[]): SearchHistoryItem[]
  updateResultCount(query: string, resultCount: number, currentItems: SearchHistoryItem[]): SearchHistoryItem[]
}
```

### 3. Infinite Scroll Support

```typescript
// Context tracks pagination state
allResults: EbayItemSummary[];      // Accumulated results
totalResults: number | null;        // Total available
hasNextPage: boolean;              // More results available
isFetchingNextPage: boolean;       // Loading state

// Load more results
{ type: 'LOAD_MORE', page?: number }
```

### 4. Type Safety Across API Boundaries

Strict type safety maintained throughout the stack:

```typescript
// Import alignment with eBay SDK
import type { ItemSummary as EbayItemSummary } from '@/generated/ebay-sdk/buy/browse/v1/api/types/ItemSummary';

// Explicit typing in services
const searchService = fromPromise<{
  itemSummaries: EbayItemSummary[];
  total: number;
  // ...
}, SearchParams>

// Type assertions where needed
const itemSummaries = searchResult.itemSummaries as EbayItemSummary[];
```

## 🛠️ Development Guidelines

### 1. Adding New Filter Types

1. Update `FilterBuilder` with new filter methods
2. Add business logic to `filterCoordinationService`
3. Update `FilterEvent` type union
4. Add URL parameter handling in `urlSyncMachine`

### 2. Extending Search Capabilities

1. Add new event types to `SearchRootEvent`
2. Implement service functions for external APIs
3. Update state machine transitions
4. Add appropriate error handling

### 3. Testing State Machines

```typescript
// Use XState testing utilities
import { waitForState } from '@/lib/state/__tests__/xstate-test-utils';

const actor = createActor(searchRootMachine).start();
actor.send({ type: 'SEARCH', data: { query: 'test' } });

await waitForState(actor, (snapshot) => 
  snapshot.value === 'searchComplete'
);
```

### 4. Debugging State Machines

```typescript
// Enable detailed logging
const rootLogger = logger.create('xstate:searchRoot');

// Log state transitions
actions: [
  ({ context, event }) => {
    rootLogger.debug('State transition', { 
      event: event.type, 
      context: context.currentQuery 
    });
  }
]
```

## 📊 Performance Considerations

### 1. Service Optimization

- **Route-based API calls**: 2x faster than server actions (1.3s vs 2.6s)
- **Debounced filter updates**: Prevent excessive API calls
- **Query cache integration**: Automatic cache invalidation on filter changes

### 2. State Machine Efficiency

- **Spawn vs. Invoke**: Child machines spawned once, not re-created
- **Context updates**: Use assign for immutable updates
- **Event batching**: URL updates batched to prevent excessive navigation

### 3. Memory Management

- **Result accumulation**: Efficient infinite scroll with array concatenation
- **History limits**: Search history capped at 50 items
- **Stack limits**: Undo/redo stacks have reasonable size limits

This architecture provides a robust, scalable, and maintainable foundation for complex search interactions while maintaining excellent user experience and performance. 