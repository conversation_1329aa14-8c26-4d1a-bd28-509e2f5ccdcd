import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { logger } from '@/lib/logger';
import { createAppBuyBrowseClient } from '@/lib/ebay/client-factory';
import { ebayBuyBrowseError } from '@/generated/ebay-sdk/buy/browse/v1/errors';
import { getMarketplaceForCountry } from '@/lib/ebay/utils/country-marketplace-mapping';
import { getLocaleForMarketplace } from '@/lib/ebay/headers';

// Import proper eBay SDK types for type safety
import type { SearchPagedCollection } from '@/generated/ebay-sdk/buy/browse/v1/api/types/SearchPagedCollection';

const log = logger.create('api:ebay:browse:search');

// Use edge runtime for optimal performance
export const runtime = 'edge';

// Validate search parameters to prevent injection
function validateSearchParams(params: URLSearchParams): { valid: boolean; error?: string } {
    const query = params.get('q');
    if (!query || query.length > 200) {
        return { valid: false, error: 'Query must be 1-200 characters' };
    }

    const filter = params.get('filter');
    if (filter && filter.length > 1000) {
        return { valid: false, error: 'Filter parameter too long' };
    }

    const limit = params.get('limit');
    if (limit && (isNaN(Number(limit)) || Number(limit) > 200 || Number(limit) < 1)) {
        return { valid: false, error: 'Limit must be 1-200' };
    }

    const offset = params.get('offset');
    if (offset && (isNaN(Number(offset)) || Number(offset) < 0)) {
        return { valid: false, error: 'Offset must be >= 0' };
    }

    return { valid: true };
}

// Define a type that represents the structure we need
interface BrowseClient {
    itemSummary: {
        search: (params: Record<string, string>) => Promise<SearchPagedCollection>;
    };
}

export async function GET(request: Request) {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    // Validate parameters
    const validation = validateSearchParams(searchParams);
    if (!validation.valid) {
        return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    if (!query) {
        return NextResponse.json({ error: 'Query parameter "q" is required' }, { status: 400 });
    }

    // Get user session for logging (authentication is optional for search)
    const session = await auth();
    const isAuthenticated = !!session?.userId;

    try {
        // Always use app credentials for Browse API search
        // User tokens provide no benefit for public search operations
        log.info('Using application credentials for search', {
            isAuthenticated,
            query: query.substring(0, 50)
        });

        const client = await createAppBuyBrowseClient('', {
            marketplaceId: 'EBAY_US',
        });

        if (!client) {
            return NextResponse.json(
                { error: 'Failed to create eBay client' },
                { status: 500 },
            );
        }

        // Build eBay search parameters with validation
        const ebaySearchParams: Record<string, string> = {};

        // Only include validated parameters
        ebaySearchParams.q = query;

        const filter = searchParams.get('filter');
        if (filter) ebaySearchParams.filter = filter;

        const sort = searchParams.get('sort');
        if (sort) ebaySearchParams.sort = sort;

        const limit = searchParams.get('limit');
        if (limit) ebaySearchParams.limit = limit;
        else ebaySearchParams.limit = '200'; // Default optimized limit

        const offset = searchParams.get('offset');
        if (offset) ebaySearchParams.offset = offset;

        // Log the search request
        log.info('Sending search request to eBay API', {
            params: ebaySearchParams,
            isAuthenticated,
        });

        // Use the correct method to search for items
        const response = await (client as unknown as BrowseClient).itemSummary.search(ebaySearchParams);

        // Log response metrics
        log.info('Received response from eBay API', {
            itemCount: response.itemSummaries?.length || 0,
            total: response.total,
            hasMoreItems: !!response.href,
            warnings: response.warnings || 'none',
        });

        // Return the response
        return NextResponse.json({
            itemSummaries: response.itemSummaries || [],
            total: response.total || 0,
            href: response.href,
            limit: response.limit,
            offset: response.offset,
            warnings: response.warnings,
        });
    } catch (error) {
        if (error instanceof ebayBuyBrowseError) {
            log.error('eBay API error', {
                error: {
                    name: error.name,
                    message: error.message,
                    statusCode: error.statusCode,
                    body: error.body,
                },
            });

            return NextResponse.json(
                { error: 'eBay API error', details: error.message },
                { status: error.statusCode || 500 },
            );
        }

        log.error('Failed to search items', {
            error:
                error instanceof Error
                    ? {
                        name: error.name,
                        message: error.message,
                        stack: error.stack,
                    }
                    : String(error),
        });

        return NextResponse.json({ error: 'Failed to search items' }, { status: 500 });
    }
} 