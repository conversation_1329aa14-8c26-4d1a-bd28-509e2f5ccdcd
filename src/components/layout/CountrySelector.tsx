'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
    DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
    getUserCountrySelection,
    getCountriesGrouped,
    setCountrySelection,
    clearCountrySelection,
    hasManualSelection,
    type CountryConfig,
    AVAILABLE_COUNTRIES
} from '@/lib/utils/country-selector';
import { type SearchActionsV3 } from '@/hooks/useSearchV3';
import {
    Globe,
    MapPin,
    RotateCcw,
    Check,
} from 'lucide-react';

interface CountrySelectorProps {
    autoDetectedCountry?: string;
    className?: string;
    variant?: 'header' | 'compact' | 'full';
    searchActions?: SearchActionsV3;
}

export function CountrySelector({
    autoDetectedCountry,
    className = '',
    variant = 'header',
    searchActions
}: CountrySelectorProps) {
    const searchParams = useSearchParams();
    const router = useRouter();
    const [currentSelection, setCurrentSelection] = useState<CountryConfig | null>(null);
    const [isManualSelection, setIsManualSelection] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const selection = getUserCountrySelection(searchParams, autoDetectedCountry);
        const isManual = hasManualSelection(searchParams);

        setCurrentSelection(selection || null);
        setIsManualSelection(isManual);
    }, [searchParams, autoDetectedCountry]);

    const handleCountryChange = (countryCode: string) => {
        const newSelection = AVAILABLE_COUNTRIES[countryCode];
        if (newSelection) {
            setIsOpen(false);

            if (searchActions) {
                // Use XState to manage country change - pass the correct postal code
                searchActions.setCountry(countryCode, newSelection.postalCode, true);

                // ALSO update URL so geolocation middleware can detect manual selection
                // This is what makes internationalization work (currency, regional sites, etc.)
                const newUrl = setCountrySelection(countryCode);
                router.push(newUrl);
            } else {
                // Fallback to URL navigation only when XState is not available
                const newUrl = setCountrySelection(countryCode);
                router.push(newUrl);
            }
        }
    };

    const handleResetToAuto = () => {
        setIsOpen(false);

        if (searchActions) {
            // Use XState to reset to auto-detected country
            const autoConfig = autoDetectedCountry ? AVAILABLE_COUNTRIES[autoDetectedCountry] : null;
            searchActions.setCountry(
                autoDetectedCountry || null,
                autoConfig?.postalCode || null,
                false
            );

            // ALSO clear URL parameter so geolocation middleware resets to auto-detection
            // This is what makes internationalization work (currency, regional sites, etc.)
            const newUrl = clearCountrySelection();
            router.push(newUrl);
        } else {
            // Fallback to URL navigation only when XState is not available
            const newUrl = clearCountrySelection();
            router.push(newUrl);
        }
    };

    const grouped = getCountriesGrouped();

    if (!currentSelection) {
        return null;
    }

    // Compact variant for mobile/small spaces
    if (variant === 'compact') {
        return (
            <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant="ghost"
                        size="sm"
                        className={`gap-1.5 px-2.5 py-1.5 sm:px-3 bg-muted/50 backdrop-blur-sm border border-border/50 hover:bg-muted/70 transition-colors ${className}`}
                    >
                        <span className="text-lg">{currentSelection.flag}</span>
                        <span className="text-xs font-medium">{currentSelection.country}</span>
                        {isManualSelection && (
                            <span className="text-xs text-amber-600 dark:text-amber-400">●</span>
                        )}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-80">
                    <CountryDropdownContent
                        grouped={grouped}
                        currentSelection={currentSelection}
                        isManualSelection={isManualSelection}
                        onCountryChange={handleCountryChange}
                        onResetToAuto={handleResetToAuto}
                    />
                </DropdownMenuContent>
            </DropdownMenu>
        );
    }

    // Header variant - shows more info
    return (
        <TooltipProvider>
            <Tooltip>
                <TooltipTrigger asChild>
                    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className={`gap-2 ${className}`}>
                                <span className="text-lg">{currentSelection.flag}</span>
                                <div className="flex flex-col items-start">
                                    <span className="text-sm font-medium">{currentSelection.countryName}</span>
                                    {isManualSelection && (
                                        <span className="text-xs text-muted-foreground">Manual</span>
                                    )}
                                </div>
                                <Globe className="h-4 w-4 text-muted-foreground" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-80">
                            <CountryDropdownContent
                                grouped={grouped}
                                currentSelection={currentSelection}
                                isManualSelection={isManualSelection}
                                onCountryChange={handleCountryChange}
                                onResetToAuto={handleResetToAuto}
                            />
                        </DropdownMenuContent>
                    </DropdownMenu>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                    <div className="space-y-1 text-xs">
                        <p className="font-medium">Browse from {currentSelection.countryName}</p>
                        <p>{currentSelection.description}</p>
                        {isManualSelection && (
                            <p className="text-amber-200">You&apos;ve overridden automatic detection</p>
                        )}
                    </div>
                </TooltipContent>
            </Tooltip>
        </TooltipProvider>
    );
}

interface CountryDropdownContentProps {
    grouped: ReturnType<typeof getCountriesGrouped>;
    currentSelection: CountryConfig;
    isManualSelection: boolean;
    onCountryChange: (countryCode: string) => void;
    onResetToAuto: () => void;
}

function CountryDropdownContent({
    grouped,
    currentSelection,
    isManualSelection,
    onCountryChange,
    onResetToAuto
}: CountryDropdownContentProps) {
    const getMarketLabel = (marketType: string) => {
        switch (marketType) {
            case 'supported': return 'eBay Marketplaces';
            case 'limited': return 'International Shipping';
            default: return 'Other Markets';
        }
    };

    return (
        <>
            <DropdownMenuLabel className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Browse eBay from
            </DropdownMenuLabel>

            {isManualSelection && (
                <>
                    <DropdownMenuItem onClick={onResetToAuto} className="text-amber-600 dark:text-amber-400">
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Reset to auto-detected location
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                </>
            )}

            {Object.entries(grouped).map(([marketType, countries]) => (
                countries.length > 0 && (
                    <React.Fragment key={marketType}>
                        <DropdownMenuLabel className="text-xs text-muted-foreground px-2 py-1">
                            {getMarketLabel(marketType)}
                        </DropdownMenuLabel>

                        {countries.map((country) => (
                            <DropdownMenuItem
                                key={country.country}
                                onClick={() => onCountryChange(country.country)}
                                className="flex items-center justify-between gap-2 p-2"
                            >
                                <div className="flex items-center gap-3 flex-1 min-w-0">
                                    <span className="text-lg">{country.flag}</span>
                                    <div className="flex flex-col flex-1 min-w-0">
                                        <div className="flex items-center gap-2">
                                            <span className="font-medium">{country.countryName}</span>
                                            {country.currency && (
                                                <span className="text-xs text-muted-foreground">({country.currency})</span>
                                            )}
                                            {currentSelection.country === country.country && (
                                                <Check className="h-4 w-4 text-green-600" />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </DropdownMenuItem>
                        ))}
                        <DropdownMenuSeparator />
                    </React.Fragment>
                )
            ))}

            <div className="p-2 text-xs text-muted-foreground">
                <p>Browse eBay from different countries to see local shipping options and pickup availability.</p>
            </div>
        </>
    );
} 